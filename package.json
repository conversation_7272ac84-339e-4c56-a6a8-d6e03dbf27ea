{"name": "curriculum_vitae", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "aos": "^2.3.4", "firebase": "^12.2.1", "framer-motion": "^12.23.14", "primeflex": "^4.0.0", "primereact": "^10.9.7", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.9.1", "typed.js": "^2.1.0"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/aos": "^3.0.7", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/typed.js": "^2.0.0", "@vitejs/plugin-react-swc": "^4.0.1", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0", "vite": "^7.1.6"}}